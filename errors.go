package main

import (
	"fmt"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger interface for pluggable logging
type Logger interface {
	Debug(msg string, fields ...zap.Field)
	Info(msg string, fields ...zap.Field)
	Warn(msg string, fields ...zap.Field)
	Error(msg string, fields ...zap.Field)
	Fatal(msg string, fields ...zap.Field)
	With(fields ...zap.Field) Logger
	Sync() error
}

// DefaultLogger wraps zap logger
type DefaultLogger struct {
	logger *zap.Logger
}

// NewDefaultLogger creates a new logger with development configuration
func NewDefaultLogger() (Logger, error) {
	logger, err := zap.NewDevelopment()
	if err != nil {
		return nil, fmt.Errorf("failed to create development logger: %w", err)
	}
	return &DefaultLogger{logger: logger}, nil
}

// NewProductionLogger creates a new logger with production configuration
func NewProductionLogger() (Logger, error) {
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("failed to create production logger: %w", err)
	}
	return &DefaultLogger{logger: logger}, nil
}

// NewCustomLogger creates a logger with custom configuration
func NewCustomLogger(level zapcore.Level, outputPaths []string) (Logger, error) {
	config := zap.Config{
		Level:       zap.NewAtomicLevelAt(level),
		Development: false,
		Sampling: &zap.SamplingConfig{
			Initial:    100,
			Thereafter: 100,
		},
		Encoding:         "json",
		EncoderConfig:    zap.NewProductionEncoderConfig(),
		OutputPaths:      outputPaths,
		ErrorOutputPaths: []string{"stderr"},
	}

	logger, err := config.Build()
	if err != nil {
		return nil, fmt.Errorf("failed to create custom logger: %w", err)
	}
	return &DefaultLogger{logger: logger}, nil
}

func (l *DefaultLogger) Debug(msg string, fields ...zap.Field) {
	if l.logger != nil {
		l.logger.Debug(msg, fields...)
	}
}
func (l *DefaultLogger) Info(msg string, fields ...zap.Field) {
	if l.logger != nil {
		l.logger.Info(msg, fields...)
	}
}
func (l *DefaultLogger) Warn(msg string, fields ...zap.Field) {
	if l.logger != nil {
		l.logger.Warn(msg, fields...)
	}
}
func (l *DefaultLogger) Error(msg string, fields ...zap.Field) {
	if l.logger != nil {
		l.logger.Error(msg, fields...)
	}
}
func (l *DefaultLogger) Fatal(msg string, fields ...zap.Field) {
	if l.logger != nil {
		l.logger.Fatal(msg, fields...)
	}
}
func (l *DefaultLogger) With(fields ...zap.Field) Logger {
	if l.logger != nil {
		return &DefaultLogger{logger: l.logger.With(fields...)}
	}
	return l
}
func (l *DefaultLogger) Sync() error {
	if l.logger != nil {
		return l.logger.Sync()
	}
	return nil
}

// NoOpLogger provides a logger that does nothing (useful for testing)
type NoOpLogger struct{}

func NewNoOpLogger() Logger {
	return &NoOpLogger{}
}

func (l *NoOpLogger) Debug(msg string, fields ...zap.Field) {}
func (l *NoOpLogger) Info(msg string, fields ...zap.Field)  {}
func (l *NoOpLogger) Warn(msg string, fields ...zap.Field)  {}
func (l *NoOpLogger) Error(msg string, fields ...zap.Field) {}
func (l *NoOpLogger) Fatal(msg string, fields ...zap.Field) {}
func (l *NoOpLogger) With(fields ...zap.Field) Logger       { return l }
func (l *NoOpLogger) Sync() error                           { return nil }

// ErrorHandler interface for pluggable error handling
type ErrorHandler interface {
	HandleError(err error, context map[string]interface{})
}

// DefaultErrorHandler provides basic error handling
type DefaultErrorHandler struct {
	logger Logger
}

func NewDefaultErrorHandler(logger Logger) ErrorHandler {
	return &DefaultErrorHandler{logger: logger}
}

func (h *DefaultErrorHandler) HandleError(err error, context map[string]interface{}) {
	fields := make([]zap.Field, 0, len(context))
	for k, v := range context {
		fields = append(fields, zap.Any(k, v))
	}
	h.logger.Error("Error occurred", append(fields, zap.Error(err))...)
}
