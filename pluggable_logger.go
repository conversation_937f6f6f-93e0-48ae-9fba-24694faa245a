package main

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"go.uber.org/zap"
)

// Field represents a key-value pair for structured logging
type Field struct {
	Key   string
	Value interface{}
}

// PluggableLogger is a truly framework-agnostic logger interface
type PluggableLogger interface {
	Debug(msg string, fields ...Field)
	Info(msg string, fields ...Field)
	Warn(msg string, fields ...Field)
	Error(msg string, fields ...Field)
	Fatal(msg string, fields ...Field)
	With(fields ...Field) PluggableLogger
	Sync() error
}

// Helper functions to create fields (similar to zap's approach)
func String(key, value string) Field {
	return Field{Key: key, Value: value}
}

func Int(key string, value int) Field {
	return Field{Key: key, Value: value}
}

func Bool(key string, value bool) Field {
	return Field{Key: key, Value: value}
}

func Error(err error) Field {
	return Field{Key: "error", Value: err}
}

func Any(key string, value interface{}) Field {
	return Field{Key: key, Value: value}
}

// ZapLogger wraps zap.Logger to implement PluggableLogger
type ZapLogger struct {
	logger *zap.Logger
}

func NewZapLogger() (PluggableLogger, error) {
	logger, err := zap.NewDevelopment()
	if err != nil {
		return nil, err
	}
	return &ZapLogger{logger: logger}, nil
}

func (l *ZapLogger) Debug(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Debug(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) Info(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Info(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) Warn(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Warn(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) Error(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Error(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) Fatal(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Fatal(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) With(fields ...Field) PluggableLogger {
	if l.logger != nil {
		return &ZapLogger{logger: l.logger.With(l.convertFields(fields)...)}
	}
	return l
}

func (l *ZapLogger) Sync() error {
	if l.logger != nil {
		return l.logger.Sync()
	}
	return nil
}

func (l *ZapLogger) convertFields(fields []Field) []zap.Field {
	zapFields := make([]zap.Field, len(fields))
	for i, field := range fields {
		switch v := field.Value.(type) {
		case string:
			zapFields[i] = zap.String(field.Key, v)
		case int:
			zapFields[i] = zap.Int(field.Key, v)
		case bool:
			zapFields[i] = zap.Bool(field.Key, v)
		case error:
			zapFields[i] = zap.Error(v)
		default:
			zapFields[i] = zap.Any(field.Key, v)
		}
	}
	return zapFields
}

// LogrusLogger wraps logrus.Logger to implement PluggableLogger
type LogrusLogger struct {
	logger *logrus.Logger
}

func NewLogrusLogger() PluggableLogger {
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})
	return &LogrusLogger{logger: logger}
}

func (l *LogrusLogger) Debug(msg string, fields ...Field) {
	l.logger.WithFields(l.convertFields(fields)).Debug(msg)
}

func (l *LogrusLogger) Info(msg string, fields ...Field) {
	l.logger.WithFields(l.convertFields(fields)).Info(msg)
}

func (l *LogrusLogger) Warn(msg string, fields ...Field) {
	l.logger.WithFields(l.convertFields(fields)).Warn(msg)
}

func (l *LogrusLogger) Error(msg string, fields ...Field) {
	l.logger.WithFields(l.convertFields(fields)).Error(msg)
}

func (l *LogrusLogger) Fatal(msg string, fields ...Field) {
	l.logger.WithFields(l.convertFields(fields)).Fatal(msg)
}

func (l *LogrusLogger) With(fields ...Field) PluggableLogger {
	return &LogrusLogger{logger: l.logger.WithFields(l.convertFields(fields)).Logger}
}

func (l *LogrusLogger) Sync() error {
	// Logrus doesn't have a sync method, but we can implement it as no-op
	return nil
}

func (l *LogrusLogger) convertFields(fields []Field) logrus.Fields {
	logrusFields := make(logrus.Fields)
	for _, field := range fields {
		logrusFields[field.Key] = field.Value
	}
	return logrusFields
}

// StdLogger wraps standard library log to implement PluggableLogger
type StdLogger struct {
	logger *log.Logger
}

func NewStdLogger() PluggableLogger {
	return &StdLogger{logger: log.New(os.Stdout, "", log.LstdFlags)}
}

func (l *StdLogger) Debug(msg string, fields ...Field) {
	l.logWithLevel("DEBUG", msg, fields...)
}

func (l *StdLogger) Info(msg string, fields ...Field) {
	l.logWithLevel("INFO", msg, fields...)
}

func (l *StdLogger) Warn(msg string, fields ...Field) {
	l.logWithLevel("WARN", msg, fields...)
}

func (l *StdLogger) Error(msg string, fields ...Field) {
	l.logWithLevel("ERROR", msg, fields...)
}

func (l *StdLogger) Fatal(msg string, fields ...Field) {
	l.logWithLevel("FATAL", msg, fields...)
	os.Exit(1)
}

func (l *StdLogger) With(fields ...Field) PluggableLogger {
	// For standard logger, we can't really implement "With" effectively,
	// so we return the same logger
	return l
}

func (l *StdLogger) Sync() error {
	// Standard logger doesn't need syncing
	return nil
}

func (l *StdLogger) logWithLevel(level, msg string, fields ...Field) {
	var fieldStrs []string
	for _, field := range fields {
		fieldStrs = append(fieldStrs, fmt.Sprintf("%s=%v", field.Key, field.Value))
	}

	fieldsStr := ""
	if len(fieldStrs) > 0 {
		fieldsStr = " [" + strings.Join(fieldStrs, " ") + "]"
	}

	l.logger.Printf("[%s] %s%s", level, msg, fieldsStr)
}

// CustomLogger demonstrates a completely custom implementation
type CustomLogger struct {
	prefix string
	level  string
}

func NewCustomLogger(prefix, level string) PluggableLogger {
	return &CustomLogger{prefix: prefix, level: level}
}

func (l *CustomLogger) Debug(msg string, fields ...Field) {
	if l.shouldLog("DEBUG") {
		l.log("DEBUG", msg, fields...)
	}
}

func (l *CustomLogger) Info(msg string, fields ...Field) {
	if l.shouldLog("INFO") {
		l.log("INFO", msg, fields...)
	}
}

func (l *CustomLogger) Warn(msg string, fields ...Field) {
	if l.shouldLog("WARN") {
		l.log("WARN", msg, fields...)
	}
}

func (l *CustomLogger) Error(msg string, fields ...Field) {
	if l.shouldLog("ERROR") {
		l.log("ERROR", msg, fields...)
	}
}

func (l *CustomLogger) Fatal(msg string, fields ...Field) {
	l.log("FATAL", msg, fields...)
	os.Exit(1)
}

func (l *CustomLogger) With(fields ...Field) PluggableLogger {
	// Create a new logger with additional context
	return &CustomLogger{prefix: l.prefix, level: l.level}
}

func (l *CustomLogger) Sync() error {
	return nil
}

func (l *CustomLogger) shouldLog(level string) bool {
	levels := map[string]int{
		"DEBUG": 0,
		"INFO":  1,
		"WARN":  2,
		"ERROR": 3,
		"FATAL": 4,
	}
	return levels[level] >= levels[l.level]
}

func (l *CustomLogger) log(level, msg string, fields ...Field) {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	var fieldStrs []string
	for _, field := range fields {
		fieldStrs = append(fieldStrs, fmt.Sprintf("%s=%v", field.Key, field.Value))
	}

	fieldsStr := ""
	if len(fieldStrs) > 0 {
		fieldsStr = " {" + strings.Join(fieldStrs, ", ") + "}"
	}

	fmt.Printf("%s [%s] %s: %s%s\n", timestamp, level, l.prefix, msg, fieldsStr)
}

// PluggableErrorHandler works with any PluggableLogger implementation
type PluggableErrorHandler struct {
	logger PluggableLogger
}

func NewPluggableErrorHandler(logger PluggableLogger) *PluggableErrorHandler {
	return &PluggableErrorHandler{logger: logger}
}

func (h *PluggableErrorHandler) HandleError(err error, context map[string]interface{}) {
	fields := make([]Field, 0, len(context)+1)
	for k, v := range context {
		fields = append(fields, Any(k, v))
	}
	fields = append(fields, Error(err))
	h.logger.Error("Error occurred", fields...)
}

// Example demonstrating pluggability
func DemonstratePluggability() {
	fmt.Println("=== Demonstrating Pluggable Logger System ===\n")

	// Test with Zap Logger
	fmt.Println("1. Using Zap Logger:")
	zapLogger, _ := NewZapLogger()
	testLogger(zapLogger)

	fmt.Println("\n2. Using Logrus Logger:")
	logrusLogger := NewLogrusLogger()
	testLogger(logrusLogger)

	fmt.Println("\n3. Using Standard Library Logger:")
	stdLogger := NewStdLogger()
	testLogger(stdLogger)

	fmt.Println("\n4. Using Custom Logger:")
	customLogger := NewCustomLogger("WebSocket", "INFO")
	testLogger(customLogger)
}

func testLogger(logger PluggableLogger) {
	// Test basic logging
	logger.Info("WebSocket manager created",
		String("version", "1.0.0"),
		Int("max_clients", 1000),
		Bool("debug_mode", true),
	)

	// Test error logging
	errorHandler := NewPluggableErrorHandler(logger)
	errorHandler.HandleError(fmt.Errorf("connection failed"), map[string]interface{}{
		"client_id": "abc-123",
		"action":    "connect",
	})

	// Test with context
	contextLogger := logger.With(String("component", "websocket"))
	contextLogger.Debug("Client connected", String("client_id", "xyz-789"))
}
