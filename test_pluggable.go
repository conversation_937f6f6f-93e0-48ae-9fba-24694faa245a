package main

import (
	"fmt"
	"os"
)

// This file demonstrates the pluggability issues and solutions

func main() {
	if len(os.Args) > 1 && os.Args[1] == "demo" {
		DemonstratePluggability()
		return
	}
	
	fmt.Println("Run with 'go run . demo' to see pluggable logger demonstration")
}

// DemonstrateProblem shows why the current Logger interface is not pluggable
func DemonstrateProblem() {
	fmt.Println("=== Current Logger Interface Problems ===\n")
	
	fmt.Println("❌ Problem 1: Zap-specific interface")
	fmt.Println("Current interface:")
	fmt.Println(`type Logger interface {
    Debug(msg string, fields ...zap.Field)  // ← zap.Field dependency
    Info(msg string, fields ...zap.Field)   // ← zap.Field dependency
    // ... other methods
}`)
	
	fmt.Println("\n❌ Problem 2: Cannot implement with other frameworks")
	fmt.Println("To implement this interface with Logrus, you would need:")
	fmt.Println(`type LogrusLogger struct { logger *logrus.Logger }

func (l *LogrusLogger) Info(msg string, fields ...zap.Field) {
    // ❌ This is impossible! Logrus doesn't use zap.Field
    // You'd be forced to import zap just to satisfy the interface
}`)
	
	fmt.Println("\n❌ Problem 3: Zap dependencies throughout codebase")
	fmt.Println("All logging calls use zap field constructors:")
	fmt.Println(`logger.Info("Client connected",
    zap.String("client_id", client.Id),  // ← zap dependency
    zap.Int("total_clients", count),     // ← zap dependency
)`)
	
	fmt.Println("\n❌ Problem 4: Error handler has zap dependencies")
	fmt.Println(`func (h *DefaultErrorHandler) HandleError(err error, context map[string]interface{}) {
    fields := make([]zap.Field, 0, len(context))  // ← zap dependency
    for k, v := range context {
        fields = append(fields, zap.Any(k, v))     // ← zap dependency
    }
    h.logger.Error("Error occurred", append(fields, zap.Error(err))...)  // ← zap dependency
}`)
}

// AttemptLogrusImplementation shows what happens when you try to implement
// the current Logger interface with Logrus
func AttemptLogrusImplementation() {
	fmt.Println("\n=== Attempting Logrus Implementation (FAILS) ===\n")
	
	fmt.Println("❌ This would fail to compile:")
	fmt.Println(`import (
    "github.com/sirupsen/logrus"
    "go.uber.org/zap"  // ← Forced to import zap!
)

type LogrusLogger struct {
    logger *logrus.Logger
}

// This method signature forces zap dependency
func (l *LogrusLogger) Info(msg string, fields ...zap.Field) {
    // How do we convert zap.Field to logrus.Fields?
    // This creates an awkward impedance mismatch
    logrusFields := make(logrus.Fields)
    for _, field := range fields {
        // ❌ zap.Field is opaque - we can't extract key/value!
        // This is impossible without reflection hacks
    }
    l.logger.WithFields(logrusFields).Info(msg)
}`)
	
	fmt.Println("\n❌ Result: Framework lock-in to Zap")
	fmt.Println("Users cannot use their preferred logging framework without:")
	fmt.Println("1. Importing zap as a dependency")
	fmt.Println("2. Writing complex conversion code")
	fmt.Println("3. Losing type safety and performance")
}

// ShowSolution demonstrates the pluggable solution
func ShowSolution() {
	fmt.Println("\n=== ✅ Solution: Framework-Agnostic Interface ===\n")
	
	fmt.Println("✅ Generic Field type:")
	fmt.Println(`type Field struct {
    Key   string
    Value interface{}
}`)
	
	fmt.Println("\n✅ Framework-agnostic interface:")
	fmt.Println(`type PluggableLogger interface {
    Debug(msg string, fields ...Field)  // ← Generic Field
    Info(msg string, fields ...Field)   // ← Generic Field
    // ... other methods
}`)
	
	fmt.Println("\n✅ Easy implementation for any framework:")
	fmt.Println(`// Zap implementation
func (l *ZapLogger) Info(msg string, fields ...Field) {
    l.logger.Info(msg, l.convertToZapFields(fields)...)
}

// Logrus implementation  
func (l *LogrusLogger) Info(msg string, fields ...Field) {
    l.logger.WithFields(l.convertToLogrusFields(fields)).Info(msg)
}

// Standard library implementation
func (l *StdLogger) Info(msg string, fields ...Field) {
    l.logWithLevel("INFO", msg, fields...)
}`)
	
	fmt.Println("\n✅ Framework-agnostic field constructors:")
	fmt.Println(`// Instead of zap.String(), zap.Int(), etc.
logger.Info("Client connected",
    String("client_id", client.Id),    // ← Generic constructor
    Int("total_clients", count),       // ← Generic constructor
)`)
}
