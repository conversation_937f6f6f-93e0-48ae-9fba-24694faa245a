package main

import (
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

type Manager struct {
	clients    map[string]*Client
	mut        sync.RWMutex
	upgrader   websocket.Upgrader
	router     *EventRouter
	errHandler ErrorHandler
	maxClients int
	config     *Config
	logger     Logger
}

// DefaultManager creates a new manager with default configuration and logger
func DefaultManager() (*Manager, error) {
	logger, err := NewDefaultLogger()
	if err != nil {
		return nil, err
	}

	manager := &Manager{
		clients: make(map[string]*Client),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Default to localhost only for security
				origin := r.Header.Get("Origin")
				allowedOrigins := []string{
					"http://localhost:3000",
					"http://localhost:8080",
					"http://127.0.0.1:3000",
					"http://127.0.0.1:8080",
				}
				for _, allowed := range allowedOrigins {
					if origin == allowed {
						return true
					}
				}
				return false
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		maxClients: 1000,
		logger:     logger,
		config:     DefaultConfig(),
	}

	manager.errHandler = NewDefaultErrorHandler(logger)

	// Log manager creation
	logger.Info("WebSocket manager created",
		zap.Int("max_clients", manager.maxClients),
		zap.Int("read_buffer_size", manager.upgrader.ReadBufferSize),
		zap.Int("write_buffer_size", manager.upgrader.WriteBufferSize),
	)

	return manager, nil
}

// CreateManager creates a new manager with custom configuration
func CreateManager(upgrader websocket.Upgrader, config *Config) (*Manager, error) {
	logger, err := NewDefaultLogger()
	if err != nil {
		return nil, err
	}

	manager := &Manager{
		clients:    make(map[string]*Client),
		upgrader:   upgrader,
		maxClients: 1000,
		config:     config,
		logger:     logger,
	}

	manager.errHandler = NewDefaultErrorHandler(logger)

	// Log manager creation
	logger.Info("Custom WebSocket manager created",
		zap.Int("max_clients", manager.maxClients),
	)

	return manager, nil
}

// SetLogger sets the logger for the manager
func (m *Manager) SetLogger(logger Logger) {
	if logger != nil {
		m.logger = logger
		// Update error handler with new logger
		if m.errHandler != nil {
			m.errHandler = NewDefaultErrorHandler(logger)
		}
		m.logger.Info("Logger updated for WebSocket manager")
	}
}

// SetErrorHandler sets the error handler for the manager
func (m *Manager) SetErrorHandler(handler ErrorHandler) {
	m.errHandler = handler
	if m.logger != nil && m.config.LogError {
		m.logger.Info("Error handler updated for WebSocket manager")
	}
}

// SetRouter sets the event router for the manager
func (m *Manager) SetRouter(router *EventRouter) {
	m.router = router
	if m.logger != nil {
		m.logger.Info("Event router set for WebSocket manager")
	}
}

// SetAllowedOrigins sets the allowed origins for CORS
func (m *Manager) SetAllowedOrigins(origins []string) {
	m.config.AllowedOrigins = origins
	m.upgrader.CheckOrigin = func(r *http.Request) bool {
		origin := r.Header.Get("Origin")
		for _, allowed := range origins {
			if strings.Contains(origin, allowed) {
				return true
			}
		}
		return false
	}

	if m.logger != nil {
		m.logger.Info("Allowed origins updated",
			zap.Strings("origins", origins),
		)
	}
}

// GetClientCount returns the current number of connected clients
func (m *Manager) GetClientCount() int {
	m.mut.RLock()
	defer m.mut.RUnlock()
	return len(m.clients)
}

func (m *Manager) AddClient(conn *websocket.Conn, online bool) *Client {
	m.mut.Lock()
	defer m.mut.Unlock()

	// Check client limit before creating client
	if len(m.clients) >= m.maxClients {
		conn.Close()
		if m.logger != nil {
			m.logger.Warn("Client connection rejected - server at capacity",
				zap.Int("current_clients", len(m.clients)),
				zap.Int("max_clients", m.maxClients),
			)
		}
		return nil
	}

	// Create client with proper close handler
	client := NewClient(conn, func(c *Client) error {
		return m.RemoveClient(c.Id)
	})

	// Set error handler
	client.SetErrHandler(func(err error) {
		if m.errHandler != nil {
			m.errHandler.HandleError(err, map[string]interface{}{
				"client_id": client.Id,
			})
		}
	})

	client.online = online
	m.clients[client.Id] = client

	// Log client connection
	if m.logger != nil && m.config.LogConnection {
		m.logger.Info("Client connected",
			zap.String("client_id", client.Id),
			zap.Bool("online", online),
			zap.Int("total_clients", len(m.clients)),
		)
	}

	return client
}

// AddExistingClient adds an existing Client instance to the manager
func (m *Manager) AddExistingClient(client *Client) bool {
	if client == nil {
		if m.logger != nil {
			m.logger.Warn("Attempted to add nil client")
		}
		return false
	}

	m.mut.Lock()
	defer m.mut.Unlock()

	// Check if client with this ID already exists
	if _, exists := m.clients[client.Id]; exists {
		if m.logger != nil {
			m.logger.Warn("Client with ID already exists",
				zap.String("client_id", client.Id),
			)
		}
		return false
	}

	// Check client limit
	if len(m.clients) >= m.maxClients {
		if m.logger != nil {
			m.logger.Warn("Cannot add existing client - server at capacity",
				zap.String("client_id", client.Id),
				zap.Int("current_clients", len(m.clients)),
				zap.Int("max_clients", m.maxClients),
			)
		}
		return false
	}

	// Add the client to the manager
	m.clients[client.Id] = client

	if m.logger != nil && m.config.LogConnection {
		m.logger.Info("Existing client added",
			zap.String("client_id", client.Id),
			zap.Int("total_clients", len(m.clients)),
		)
	}

	return true
}

// RemoveClient removes a client from the manager
func (m *Manager) RemoveClient(clientId string) error {
	m.mut.Lock()
	defer m.mut.Unlock()

	if client, exists := m.clients[clientId]; exists {
		client.Close()
		delete(m.clients, clientId)

		if m.logger != nil && m.config.LogConnection {
			m.logger.Info("Client disconnected",
				zap.String("client_id", clientId),
				zap.Int("remaining_clients", len(m.clients)),
			)
		}
	} else {
		if m.logger != nil {
			m.logger.Debug("Attempted to remove non-existent client",
				zap.String("client_id", clientId),
			)
		}
	}
	return nil
}

// GetClient returns a client by ID
func (m *Manager) GetClient(clientId string) (*Client, bool) {
	m.mut.RLock()
	defer m.mut.RUnlock()
	client, exists := m.clients[clientId]
	return client, exists
}

// BroadcastMessage sends a message to all connected clients
func (m *Manager) BroadcastMessage(msg *Message) {
	m.mut.RLock()
	defer m.mut.RUnlock()

	sentCount := 0
	for _, client := range m.clients {
		if client.IsOnline() {
			client.Send(msg)
			sentCount++
		}
	}

	if m.logger != nil {
		m.logger.Debug("Message broadcasted",
			zap.String("event", msg.Event),
			zap.Int("recipients", sentCount),
			zap.Int("total_clients", len(m.clients)),
		)
	}
}

func (m *Manager) BroadcastMessageToGroup(msg *Message, group []string) {
	m.mut.RLock()
	defer m.mut.RUnlock()

	sentCount := 0
	offlineCount := 0

	for _, id := range group {
		client, exists := m.clients[id]
		if !exists {
			continue
		}
		if client.IsOnline() {
			client.Send(msg)
			sentCount++
		} else {
			offlineCount++
			if m.logger != nil {
				m.logger.Debug("Client offline, skipping broadcast",
					zap.String("client_id", id),
					zap.String("event", msg.Event),
				)
			}
		}
	}

	if m.logger != nil {
		m.logger.Debug("Group message broadcasted",
			zap.String("event", msg.Event),
			zap.Int("recipients", sentCount),
			zap.Int("offline_clients", offlineCount),
			zap.Int("group_size", len(group)),
		)
	}
}

func (m *Manager) ReadPump(client *Client) {
	defer func() {
		client.Close()
		m.RemoveClient(client.Id)
		if client.cancel != nil {
			client.cancel()
		}
	}()

	// Set read deadline and pong handler for keepalive
	client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.conn.SetPongHandler(func(string) error {
		client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		if m.logger != nil {
			m.logger.Debug("Pong received",
				zap.String("client_id", client.Id),
			)
		}
		return nil
	})

	if m.logger != nil {
		m.logger.Debug("ReadPump started",
			zap.String("client_id", client.Id),
		)
	}

	for {
		select {
		case <-client.ctx.Done():
			if m.logger != nil {
				m.logger.Debug("ReadPump context cancelled",
					zap.String("client_id", client.Id),
				)
			}
			return
		default:
			_, msg, err := client.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					if m.logger != nil && m.config.LogError {
						m.logger.Error("WebSocket unexpected close error",
							zap.String("client_id", client.Id),
							zap.Error(err),
						)
					}
				} else {
					if m.logger != nil {
						m.logger.Debug("WebSocket connection closed",
							zap.String("client_id", client.Id),
							zap.Error(err),
						)
					}
				}
				return
			}

			message, err := NewMessageFromBytes(msg)
			if err != nil {
				if m.errHandler != nil {
					m.errHandler.HandleError(err, map[string]interface{}{
						"client_id": client.Id,
						"action":    "parse_message",
						"raw_data":  string(msg),
					})
				}
				continue
			}

			if m.logger != nil {
				m.logger.Debug("Message received",
					zap.String("client_id", client.Id),
					zap.String("event", message.Event),
					zap.Int("data_size", len(message.Data)),
				)
			}

			// Create context for handling the incoming message
			ctx := Ctx{
				client:  client,
				manager: m,
				Data:    message,
				ctx:     client.ctx,
			}

			// Handle the message through the router
			if m.router != nil {
				err := m.router.Handle(ctx)
				if err != nil {
					if m.errHandler != nil {
						m.errHandler.HandleError(err, map[string]interface{}{
							"client_id": client.Id,
							"event":     message.Event,
							"action":    "handle_message",
						})
					}
				} else {
					if m.logger != nil {
						m.logger.Debug("Message handled successfully",
							zap.String("client_id", client.Id),
							zap.String("event", message.Event),
						)
					}
				}
			} else {
				if m.logger != nil {
					m.logger.Warn("No router configured, message ignored",
						zap.String("client_id", client.Id),
						zap.String("event", message.Event),
					)
				}
			}
		}
	}
}

func (m *Manager) WritePump(client *Client) {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		m.RemoveClient(client.Id)
		if client.cancel != nil {
			client.cancel()
		}
	}()

	if m.logger != nil {
		m.logger.Debug("WritePump started",
			zap.String("client_id", client.Id),
		)
	}

	for {
		select {
		case <-client.ctx.Done():
			if m.logger != nil {
				m.logger.Debug("WritePump context cancelled",
					zap.String("client_id", client.Id),
				)
			}
			return

		case msg, ok := <-client.send:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.conn.WriteMessage(websocket.CloseMessage, []byte{})
				if m.logger != nil {
					m.logger.Debug("Send channel closed, sending close message",
						zap.String("client_id", client.Id),
					)
				}
				return
			}

			// Marshal message to JSON and write to WebSocket
			if err := client.conn.WriteJSON(msg); err != nil {
				if m.errHandler != nil {
					m.errHandler.HandleError(err, map[string]interface{}{
						"client_id": client.Id,
						"event":     msg.Event,
						"action":    "write_message",
					})
				}
				return
			}

			if m.logger != nil {
				m.logger.Debug("Message sent",
					zap.String("client_id", client.Id),
					zap.String("event", msg.Event),
				)
			}

		case <-ticker.C:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				if m.logger != nil && m.config.LogError {
					m.logger.Error("Failed to send ping",
						zap.String("client_id", client.Id),
						zap.Error(err),
					)
				}
				return
			}
			if m.logger != nil && m.config.EnablePing {
				m.logger.Debug("Ping sent",
					zap.String("client_id", client.Id),
				)
			}
		}
	}
}

type Config struct {
	ReadTimeout       time.Duration `json:"read_timeout"`
	WriteTimeout      time.Duration `json:"write_timeout"`
	PingInterval      time.Duration `json:"ping_interval"`
	PongTimeout       time.Duration `json:"pong_timeout"`
	ReadBufferSize    int           `json:"read_buffer_size"`
	WriteBufferSize   int           `json:"write_buffer_size"`
	SendChannelSize   int           `json:"send_channel_size"`
	MaxClients        int           `json:"max_clients"`
	MaxMessageSize    int           `json:"max_message_size"`
	AllowedOrigins    []string      `json:"allowed_origins"`
	EnableCompression bool          `json:"enable_compression"`
	EnablePing        bool          `json:"enable_ping"`
	LogConnection     bool          `json:"log_connection"`
	LogError          bool          `json:"log_error"`
}

func DefaultConfig() *Config {
	return &Config{
		ReadTimeout:  60 * time.Second,
		WriteTimeout: 10 * time.Second,
		PingInterval: 54 * time.Second,
		PongTimeout:  10 * time.Second,

		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		SendChannelSize: 100,

		MaxClients:     1000,
		MaxMessageSize: 1024 * 1024, // 1 MB
		AllowedOrigins: []string{"http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"},

		EnableCompression: false,
		EnablePing:        true,
		LogConnection:     true,
		LogError:          true,
	}
}
