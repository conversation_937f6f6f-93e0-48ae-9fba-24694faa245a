package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	// Create manager with proper configuration
	manager, err := DefaultManager()
	manager.SetAllowedOrigins([]string{"*"})
	if err != nil {
		panic("Failed to create manager: " + err.Error())
	}

	// Ensure logger is properly synced on exit
	defer func() {
		if manager.logger != nil {
			manager.logger.Sync()
		}
	}()

	// Set up router with basic handlers
	router := NewEventRouter()
	router.On("ping", handlePing)
	router.On("echo", handleEcho)
	manager.SetRouter(router)

	// HTTP handler for WebSocket upgrade
	http.HandleFunc("/ws", func(w http.ResponseWriter, r *http.Request) {
		handleWebSocket(manager, w, r)
	})

	// Start HTTP server
	server := &http.Server{
		Addr:         ":8080",
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	go func() {
		if manager.logger != nil {
			manager.logger.Info("WebSocket server starting",
				String("address", ":8080"),
			)
		}
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			if manager.logger != nil {
				manager.logger.Fatal("Server failed to start",
					Error(err),
				)
			}
		}
	}()

	// Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	if manager.logger != nil {
		manager.logger.Info("Shutting down server...")
	}
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		if manager.logger != nil {
			manager.logger.Fatal("Server forced to shutdown",
				Error(err),
			)
		}
	}

	if manager.logger != nil {
		manager.logger.Info("Server exited")
	}
}

func handleWebSocket(manager *Manager, w http.ResponseWriter, r *http.Request) {
	conn, err := manager.upgrader.Upgrade(w, r, nil)
	if err != nil {
		if manager.logger != nil && manager.config.LogError {
			manager.logger.Error("WebSocket upgrade failed",
				Error(err),
				String("remote_addr", r.RemoteAddr),
				String("user_agent", r.UserAgent()),
			)
		}
		return
	}

	client := manager.AddClient(conn, true)
	if client == nil {
		if manager.logger != nil {
			manager.logger.Warn("Failed to add client - server at capacity",
				String("remote_addr", r.RemoteAddr),
			)
		}
		conn.Close()
		return
	}

	// Start client goroutines
	go manager.ReadPump(client)
	go manager.WritePump(client)
}

// Basic event handlers
func handlePing(c Ctx) error {
	response := NewMessage("pong", []byte(`{"message":"pong"}`))
	return c.WriteMessage(response)
}

func handleEcho(c Ctx) error {
	// Echo back the received data
	response := NewMessage("echo", c.GetMsg())
	return c.WriteMessage(response)
}
